# 🚀 Grok Chat Interface

A Streamlit-based chat application that provides a user-friendly interface for interacting with xAI's Grok models, featuring web search integration and Model Context Protocol (MCP) support.

## 💡 Features

- 💬 **Interactive Chat Interface**: Clean and intuitive UI for conversing with Grok models
- 📚 **Conversation Management**: Save, rename, and delete conversations
- 🌐 **Enhanced Web Search Integration**:
  - Advanced Brave Search integration with intelligent query processing
  - Automatic result clustering and source attribution
  - Freshness filtering for time-sensitive queries
  - Automatic detection of news-related queries for improved handling
  - References section with citations in responses
  - Date awareness in AI responses
- ⚙️ **Customizable Settings**:
  - ~~Adjustable reasoning effort (Low, Medium, High)~~ *Deprecated: No longer supported by newer xAI models*
  - Option to view the model's reasoning process
  - Dynamic model selection from available xAI models via API
- 🔌 **Model Context Protocol (MCP) Support**:
  - Configure and use MCP to enhance AI capabilities with external tools
  - Simple UI for enabling/disabling individual MCP servers
  - Transparent tool usage indicators during processing and in responses
  - Dynamic tool discovery and intelligent tool selection based on query intent
  - Support for explicit tool requests in queries (e.g., "Use firecrawl to scrape...")
  - Support for multiple MCP servers including Brave Search, Perplexity, Tavily, Firecrawl, and more
- ➗ **LaTeX Support**: Proper rendering of mathematical expressions and equations
- 📋 **Copy Functionality**: Easy copying of assistant responses via expandable sections
- 🎨 **Official xAI Branding**: Uses the official xAI icon from x.ai/api

## 🔧 Installation

1. Clone this repository
2. Install the required dependencies:

```bash
pip install -r requirements.txt
```

Note: Make sure all dependencies are properly installed before running the application.

## ⚙️ Environment Setup

Set the following environment variables:

- `XAI_API_KEY`: Your xAI API key for accessing Grok models
- `BRAVE_API_KEY`: Your Brave Search API key (optional, for web search functionality)

For MCP configuration, you can create a `@mcp_settings.json` file (recommended for security) or use the default `mcp_settings.json` file. The `@` prefix ensures your API keys remain private and excluded from version control.

## 💻 Usage

Run the application with:

```bash
streamlit run app.py
```

The application will be available at `http://localhost:8501` by default.

### ⚠️ Troubleshooting

If you encounter any errors when running the application:

1. Make sure all dependencies are installed correctly:
   ```bash
   pip install -r requirements.txt
   ```

2. Verify that your environment variables are set correctly.

3. Check that your xAI API key has access to the models you're trying to use.

4. For MCP-related issues:
   - Ensure your `mcp_settings.json` or `@mcp_settings.json` file is properly formatted
   - Check that the MCP servers you're trying to use are enabled
   - Verify that any required API keys for MCP servers are correctly set
   - If a specific tool isn't working, check the server configuration in the MCP settings file

## ⚙️ Configuration

The application stores settings in `settings.json` and conversations in `conversations.json`. These files are automatically created and managed by the application.

### MCP Configuration

The application supports Model Context Protocol (MCP) for enhanced AI capabilities:

1. Enable MCP using the toggle in the sidebar
2. Configure MCP settings by clicking on the "MCP Settings" expander
3. Toggle individual MCP servers on or off directly in the UI
4. For advanced configuration, edit the `mcp_settings.json` file directly

The MCP settings are stored in `mcp_settings.json` or `@mcp_settings.json` (for private settings) in the same format as Claude's MCP configuration. For security reasons, these files are excluded from version control as they may contain API keys.

#### MCP Server Management

- **Simple Configuration**: Toggle MCP servers on/off directly in the UI
- **Advanced Configuration**: Edit the `mcp_settings.json` file for detailed settings
- **Server Descriptions**: Hover over server names to see descriptions of their capabilities
- **Custom Descriptions**: Add a `description` field to each server in `mcp_settings.json` to customize descriptions

#### MCP Tool Usage

- **Dynamic Tool Discovery**: The application automatically discovers available tools from enabled MCP servers
- **Intelligent Tool Selection**: Tools are selected based on query intent and content
- **Explicit Tool Requests**: You can explicitly request specific tools by including "Use [tool-name]" in your query
- **Tool Usage Indicators**: The application shows which tools are being used during processing
- **Tool Results**: Tool results are incorporated into the AI's response with proper attribution

### Web Search Integration

The application integrates with Brave Search to provide more informed responses:

1. Enable Web Search using the toggle in the sidebar (your preference is saved)
2. The AI will automatically determine when to use search based on your query
3. Search results are processed and incorporated into the response
4. A references section is added to responses with citations to the sources used

The web search setting is stored in `settings.json` along with other application preferences, ensuring your choice persists between sessions.

## ✅ Requirements

- Python 3.7+
- Streamlit
- OpenAI Python client (for xAI API compatibility)
- Requests

All dependencies are listed in the `requirements.txt` file. The application uses a minimal set of dependencies to ensure easy installation and compatibility.

### 📋 Copy Functionality

The application uses Streamlit's built-in components to provide copy functionality:
- Each assistant response includes an expandable "Copy this response" section
- When expanded, the response is displayed in a code block with a built-in copy button
- This approach ensures reliable copying across different environments and browsers

## 📜 License

MIT

## 👏 Acknowledgements

- xAI for providing the Grok models and API
- Brave Search for web search capabilities
- Streamlit for the web application framework
- Model Context Protocol (MCP) for enhanced AI capabilities
- Firecrawl for web scraping and crawling capabilities
- Perplexity, Tavily, and Serper for additional search and research capabilities
- Augment for AI code assistance
