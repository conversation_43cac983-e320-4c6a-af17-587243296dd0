#!/usr/bin/env python3
"""
Test script to verify that the reasoningEffort parameter fix works.
This script tests the API call without the deprecated parameter.
"""

import os
from openai import OpenAI

def test_xai_api_call():
    """Test the xAI API call without the reasoningEffort parameter."""
    
    # Check if API key is set
    api_key = os.getenv("XAI_API_KEY")
    if not api_key:
        print("❌ Error: XAI_API_KEY environment variable not set")
        return False
    
    try:
        # Create OpenAI client configured for xAI
        client = OpenAI(
            api_key=api_key,
            base_url="https://api.x.ai/v1"
        )
        
        # Test message
        messages = [
            {"role": "system", "content": "You are a helpful AI assistant."},
            {"role": "user", "content": "Hello! Can you tell me what 2+2 equals?"}
        ]
        
        print("🧪 Testing xAI API call without reasoningEffort parameter...")
        
        # Make API call without the deprecated reasoningEffort parameter
        completion = client.chat.completions.create(
            model="grok-3-mini-beta",  # Use a known working model
            messages=messages,
            temperature=0.3,
        )
        
        # Check if we got a response
        if completion.choices and completion.choices[0].message:
            response_content = completion.choices[0].message.content
            print("✅ API call successful!")
            print(f"📝 Response: {response_content[:100]}...")
            return True
        else:
            print("❌ API call returned empty response")
            return False
            
    except Exception as e:
        error_msg = str(e)
        if "reasoningEffort" in error_msg:
            print(f"❌ Still getting reasoningEffort error: {error_msg}")
        else:
            print(f"❌ API call failed with different error: {error_msg}")
        return False

def test_with_newer_model():
    """Test with a newer model that definitely doesn't support reasoningEffort."""
    
    api_key = os.getenv("XAI_API_KEY")
    if not api_key:
        print("❌ Error: XAI_API_KEY environment variable not set")
        return False
    
    try:
        client = OpenAI(
            api_key=api_key,
            base_url="https://api.x.ai/v1"
        )
        
        messages = [
            {"role": "system", "content": "You are a helpful AI assistant."},
            {"role": "user", "content": "What is the capital of France?"}
        ]
        
        print("🧪 Testing with newer model (grok-4-0709)...")
        
        # Test with the model mentioned in settings.json
        completion = client.chat.completions.create(
            model="grok-4-0709",
            messages=messages,
            temperature=0.3,
        )
        
        if completion.choices and completion.choices[0].message:
            response_content = completion.choices[0].message.content
            print("✅ Newer model API call successful!")
            print(f"📝 Response: {response_content[:100]}...")
            return True
        else:
            print("❌ Newer model API call returned empty response")
            return False
            
    except Exception as e:
        error_msg = str(e)
        if "reasoningEffort" in error_msg:
            print(f"❌ Still getting reasoningEffort error with newer model: {error_msg}")
        elif "model" in error_msg.lower():
            print(f"⚠️  Model not available, but no reasoningEffort error: {error_msg}")
            return True  # This is actually good - means the parameter issue is fixed
        else:
            print(f"❌ API call failed with different error: {error_msg}")
        return False

if __name__ == "__main__":
    print("🔧 Testing xAI API fix for reasoningEffort parameter removal")
    print("=" * 60)
    
    # Test with default model
    success1 = test_xai_api_call()
    print()
    
    # Test with newer model
    success2 = test_with_newer_model()
    print()
    
    if success1 or success2:
        print("🎉 Fix appears to be working! The reasoningEffort parameter has been successfully removed.")
    else:
        print("❌ Fix may not be complete. Please check the error messages above.")
